<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Three.js Meshes Control Page</title>
  <link rel="stylesheet" href="style.css">
</head>
<body>
  <div class="container">
    <!-- Left: 3D Canvas -->
    <div class="canvas-section">
      <canvas id="three-canvas"></canvas>
    </div>
    <!-- Right: Controls -->
    <div class="controls-section">
      <button id="rotateA">Rotate Model A</button>
      <button id="rotateB">Rotate Model B</button>
      <button id="focus">Switch Focus</button>
      <div id="focus-info"></div>
    </div>
  </div>
  <script src="https://unpkg.com/three@0.153.0/build/three.min.js"></script>
  <script src="script.js"></script>
</body>
</html>
