// --- Three.js Scene Setup ---
const canvas = document.getElementById('three-canvas');
const renderer = new THREE.WebGLRenderer({ canvas, antialias: true });
renderer.setSize(canvas.parentElement.clientWidth, canvas.parentElement.clientHeight);

const scene = new THREE.Scene();
scene.background = new THREE.Color(0x333333);
const camera = new THREE.PerspectiveCamera(60, canvas.parentElement.clientWidth/canvas.parentElement.clientHeight, 0.1, 1000);
camera.position.set(0, 2, 6); // Constant position

// --- Meshes ---
const materialA = new THREE.MeshStandardMaterial({ color: 0x0099ff });
const materialB = new THREE.MeshStandardMaterial({ color: 0xff9900 });
const materialC = new THREE.MeshStandardMaterial({ color: 0x88ff88 });

const geometryA = new THREE.SphereGeometry(0.6, 32, 32);
const geometryB = new THREE.BoxGeometry(1.1, 1.1, 1.1);
const geometryC = new THREE.CylinderGeometry(0.7, 0.7, 0.25, 32);

const meshA = new THREE.Mesh(geometryA, materialA);     // Model A
const meshB = new THREE.Mesh(geometryB, materialB);     // Model B
const meshC = new THREE.Mesh(geometryC, materialC);     // Model C (constant)

meshC.position.set(0, 2, 0);   // Top - Model C
meshA.position.set(-1.2, 0, 0); // Initial - Model A left bottom
meshB.position.set(1.2, 0, 0);  // Initial - Model B right bottom

scene.add(meshA, meshB, meshC);

// Lighting
const light = new THREE.DirectionalLight(0xffffff, 1.1);
light.position.set(5,6,11);
scene.add(light);

// --- Interaction State ---
let focused = 'A'; // 'A' or 'B'

// --- Resize Handling ---
window.addEventListener('resize', () => {
  renderer.setSize(canvas.parentElement.clientWidth, canvas.parentElement.clientHeight);
  camera.aspect = canvas.parentElement.clientWidth/canvas.parentElement.clientHeight;
  camera.updateProjectionMatrix();
});

// --- Animation Loop ---
function animate() {
  renderer.render(scene, camera);
  requestAnimationFrame(animate);
}
animate();

// --- Button Controls ---
const rotateA = document.getElementById('rotateA');
const rotateB = document.getElementById('rotateB');
const focusBtn = document.getElementById('focus');
const focusInfo = document.getElementById('focus-info');

function updateFocusDisplay() {
  focusInfo.textContent = `Focus: Model ${focused}`;
}
updateFocusDisplay();

rotateA.onclick = () => {
  meshA.rotation.y += Math.PI / 4; // Rotate Model A
};
rotateB.onclick = () => {
  meshB.rotation.y += Math.PI / 4; // Rotate Model B
};

focusBtn.onclick = () => {
  if (focused === 'A') {
    // Move Model B to center, A to side
    meshA.position.set(-1.2, 0, 0);
    meshB.position.set(0, 0, 0);
    focused = 'B';
  } else {
    meshA.position.set(0, 0, 0);
    meshB.position.set(1.2, 0, 0);
    focused = 'A';
  }
  updateFocusDisplay();
};
